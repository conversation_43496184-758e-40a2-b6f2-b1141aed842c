import { useContext, useState, useEffect } from 'react';
import { ConfigContext } from "@/context/ConfigContext";
import { BusinessContext } from "@/context/BusinessContext";
import { TeamProvider, TeamContext } from "@/context/TeamContext";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Plus, Users, Edit, Trash2, UserPlus } from "lucide-react";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { postToApi } from '@/lib/api';
import { ISessionUser, IBusinessTeamRes, ILocationTeamRes } from "@/types";
import { AddTeamMemberDialog } from '@/components/teams/AddTeamMemberDialog';
import { useToast } from "@/components/ui/use-toast";

export function TeamPage() {
  return (
    <TeamProvider>
      <TeamComponent />
    </TeamProvider>
  );
}

function TeamComponent() {
  const { user }: { 
    user: ISessionUser 
  } = useContext(ConfigContext);
  const { provider, setupUser } : {
    provider: any,
    setupUser: any 
  } = useContext(BusinessContext);
  const { businessTeam, locationTeam, getBusinessTeam, deleteTeamMember }: {
    businessTeam: IBusinessTeamRes[],
    locationTeam: ILocationTeamRes[],
    getBusinessTeam: (user: ISessionUser, business: any, location: any) => void,
    deleteTeamMember: (user: ISessionUser, member: any, location: any) => void
  } = useContext(TeamContext);
  const { toast } = useToast();

  const [isAddTeamDialogOpen, setIsAddTeamDialogOpen] = useState(false);
  const [memberToDelete, setMemberToDelete] = useState(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  useEffect(() => {
    setupUser(user);
    if (provider && businessTeam.length == 0) {
      getBusinessTeam(user, provider, provider.locations[0]);
    }
  }, [provider, user]);

  const handleDeleteClick = (member) => {
    setMemberToDelete(member);
    setDeleteDialogOpen(true);
  };

  const confirmDelete = () => {
    if (memberToDelete) {
      deleteTeamMember(user, memberToDelete, provider.locations[0]);
      toast({
        title: "Member Deleted",
        description: `Deleted ${memberToDelete.Name} from the team`,
      });
      setMemberToDelete(null);
    }
    setDeleteDialogOpen(false);
  };

  const cancelDelete = () => {
    setMemberToDelete(null);
    setDeleteDialogOpen(false);
  };

  const handleAddMember = (newMember) => {
    if (businessTeam.some((member) => member.roleId === newMember.id)
      || locationTeam.some((member) => member.roleId === newMember.id)) {
        toast({
          title: "Member Already in Team",
          description: `User ${newMember.name} is already in the team`,
          variant: "destructive",
        });
        return;
    }

    const url = "/api/v1/business/role"
    const data = {
      roleId: newMember.id,
      businessId: provider.id,
      locationId: provider.locations[0].id,
      type: newMember.type
    }
    postToApi(url, user, data,
      (result) => {
        getBusinessTeam(user, provider, provider.locations[0]);
        toast({
          title: "Member Added",
          description: `Added ${newMember.name} to the team`,
        });
      },
      (error) => {
        console.log("error", error)
      }
    )
  };


  return (
    <>
      <div className="space-y-6 p-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold">Team</h1>
            <p className="text-muted-foreground">View and manage team members.</p>
          </div>
          <Button
            className="flex items-center gap-2"
            onClick={() => setIsAddTeamDialogOpen(true)}
          >
            <UserPlus className="h-4 w-4" />
            Add Team Member
          </Button>
        </div>

        {/* Business Team Table */}
        <div>
          <h2 className="text-xl font-semibold mb-4">Business Team</h2>
          {businessTeam && businessTeam.length > 0 ? (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>UID</TableHead>
                    <TableHead>Name</TableHead>
                    <TableHead>Permission</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {businessTeam.map((member) => (
                    <TeamMemberItem
                      key={member.id}
                      member={member}
                      onEdit={() => { }}
                      onDelete={() => handleDeleteClick(member)}
                    />
                  ))}
                </TableBody>
              </Table>
            </div>
          ) : (
            <div className="text-center py-8 border rounded-md bg-muted/10">
              <div className="flex justify-center mb-2">
                <Users className="h-10 w-10 text-muted-foreground" />
              </div>
              <p className="text-muted-foreground mb-2">No business team members found.</p>
              <Button size="sm" variant="outline" className="flex items-center gap-2 mx-auto">
                <Plus className="h-4 w-4" />
                Add First Team Member
              </Button>
            </div>
          )}
        </div>

        <Separator className="my-6" />

        {/* Location Team Table */}
        <div>
          <h2 className="text-xl font-semibold mb-4">Location Team</h2>
          {locationTeam && locationTeam.length > 0 ? (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>UID</TableHead>
                    <TableHead>Name</TableHead>
                    <TableHead>Permission</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {locationTeam.map((member) => (
                    <TeamMemberItem
                      key={member.id}
                      member={member}
                      onEdit={() => { }}
                      onDelete={() => handleDeleteClick(member)}
                    />
                  ))}
                </TableBody>
              </Table>
            </div>
          ) : (
            <div className="text-center py-8 border rounded-md bg-muted/10">
              <div className="flex justify-center mb-2">
                <Users className="h-10 w-10 text-muted-foreground" />
              </div>
              <p className="text-muted-foreground mb-2">No location team members found.</p>
              <Button size="sm" variant="outline" className="flex items-center gap-2 mx-auto">
                <Plus className="h-4 w-4" />
                Add First Location Team Member
              </Button>
            </div>
          )}
        </div>

        {/* Delete Confirmation Dialog */}
        <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Confirm Deletion</AlertDialogTitle>
              <AlertDialogDescription>
                Are you sure you want to remove {memberToDelete?.name || memberToDelete?.Name || 'this team member'}?
                This action cannot be undone.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel onClick={cancelDelete}>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={confirmDelete}
                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              >
                Delete
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
      {isAddTeamDialogOpen &&
        <AddTeamMemberDialog
          isOpen={isAddTeamDialogOpen}
          setIsOpen={() => setIsAddTeamDialogOpen(false)}
          onUserSelected={handleAddMember}
        />
      }
    </>
  );
}

function TeamMemberItem({ member, onEdit, onDelete }) {
  // Get permission badge color based on permission level
  const getTypeBadge = (permission) => {
    switch (permission?.toLowerCase()) {
      case 'admin':
        return <Badge className="bg-red-500">Admin</Badge>;
      case 'manager':
        return <Badge className="bg-blue-500">Manager</Badge>;
      case 'technician':
        return <Badge className="bg-green-500">Technician</Badge>;
      case 'viewer':
        return <Badge className="bg-gray-500">Viewer</Badge>;
      default:
        return <Badge className="bg-gray-400">{permission || 'Unknown'}</Badge>;
    }
  };

  return (
    <TableRow>
      <TableCell className="font-medium">{member.id || 'N/A'}</TableCell>
      <TableCell>{member.Name || member.name || 'N/A'}</TableCell>
      <TableCell>{member.permissions}</TableCell>
      <TableCell>{getTypeBadge(member.type || 'Staff')}</TableCell>
      <TableCell className="text-right">
        <div className="flex justify-end gap-2">
          <Button variant="ghost" size="icon" className="h-8 w-8" onClick={onEdit}>
            <Edit className="h-4 w-4" />
            <span className="sr-only">Edit</span>
          </Button>
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8 text-destructive"
            onClick={(e) => {
              e.stopPropagation();
              onDelete();
            }}
          >
            <Trash2 className="h-4 w-4" />
            <span className="sr-only">Delete</span>
          </Button>
        </div>
      </TableCell>
    </TableRow>
  );
}