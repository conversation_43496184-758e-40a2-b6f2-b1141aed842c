import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Di<PERSON>Header, <PERSON><PERSON>Title, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Checkbox } from "@/components/ui/checkbox";
import { MultiSelect } from "@/components/ui/multi-select";
import { Plus, Trash2 } from "lucide-react";
import { IInvoiceRes } from '@/types';

// Enums for field types and sources
export enum FieldType {
  STRING = "string",
  NUMBER = "number",
  BOOL = "bool",
  DATE = "date",
  SELECT = "select",
  TEXTAREA = "textarea"
}

export enum FieldFormat {
  TEXT = "text",
  NUMBER = "number",
  DATE = "date",
  BOOL = "bool",
  SELECT = "select",
  TEXTAREA = "textarea"
}

export enum FieldSource {
  GENERAL = "General",
  DATES_TERMS = "DatesTerms",
  CUSTOMER = "Customer",
  ITEMS = "Items",
  FINANCIAL = "Financial",
  PAYMENT = "Payment",
  PROVIDER = "Provider",
  ATTACHMENTS = "Attachments"
}

interface IInvoiceField {
  name: string;
  description: string;
  type: FieldType;
  format: FieldFormat;
  source: FieldSource;
  index: number;
  value?: any;
  options?: { label: string; value: any }[];
  required?: boolean;
  readOnly?: boolean;
}
// Enums for invoice status and payment methods
export enum InvoiceStatus {
  DRAFT = "draft",
  SENT = "sent",
  PAID = "paid",
  OVERDUE = "overdue"
}

export enum PaymentMethod {
  CASH = "cash",
  CHECK = "check",
  CREDIT_CARD = "credit_card",
  BANK_TRANSFER = "bank_transfer",
  OTHER = "other"
}

// Options arrays derived from enums
const INVOICE_STATUS_OPTIONS = [
  { label: "Draft", value: InvoiceStatus.DRAFT },
  { label: "Sent", value: InvoiceStatus.SENT },
  { label: "Paid", value: InvoiceStatus.PAID },
  { label: "Overdue", value: InvoiceStatus.OVERDUE }
];

const PAYMENT_METHOD_OPTIONS = [
  { label: "Cash", value: PaymentMethod.CASH },
  { label: "Check", value: PaymentMethod.CHECK },
  { label: "Credit Card", value: PaymentMethod.CREDIT_CARD },
  { label: "Bank Transfer", value: PaymentMethod.BANK_TRANSFER },
  { label: "Other", value: PaymentMethod.OTHER }
];

const FLAG_OPTIONS = [
  { label: "Urgent", value: "urgent" },
  { label: "Recurring", value: "recurring" },
  { label: "Created by Provider", value: "createdByProvider" },
  { label: "Paid", value: "paid" }
];

function getInvoiceFields(invoice: IInvoiceRes | null): IInvoiceField[] {
  if (!invoice) return [];

  const baseFields: IInvoiceField[] = [
    // 1. General Invoice Information
    {
      name: 'name',
      description: 'Invoice Name',
      type: FieldType.STRING,
      format: FieldFormat.TEXT,
      source: FieldSource.GENERAL,
      index: 1,
      value: invoice.name || ''
    },
    {
      name: 'number',
      description: 'Invoice Number',
      type: FieldType.STRING,
      format: FieldFormat.TEXT,
      source: FieldSource.GENERAL,
      index: 2,
      value: invoice.number || '',
      required: true
    },
    {
      name: 'Num',
      description: 'Invoice Num',
      type: FieldType.NUMBER,
      format: FieldFormat.NUMBER,
      source: FieldSource.GENERAL,
      index: 3,
      value: invoice.Num || 0
    },
    {
      name: 'status',
      description: 'Status',
      type: FieldType.STRING,
      format: FieldFormat.SELECT,
      source: FieldSource.GENERAL,
      index: 4,
      value: invoice.status || InvoiceStatus.DRAFT,
      options: INVOICE_STATUS_OPTIONS,
      required: true
    },
    {
      name: 'flags',
      description: 'Flags',
      type: FieldType.STRING,
      format: FieldFormat.SELECT,
      source: FieldSource.GENERAL,
      index: 5,
      value: invoice.flags || [],
      options: FLAG_OPTIONS
    },
    {
      name: 'notes',
      description: 'Notes',
      type: FieldType.STRING,
      format: FieldFormat.TEXTAREA,
      source: FieldSource.GENERAL,
      index: 6,
      value: invoice.notes || ''
    },
    {
      name: 'reference',
      description: 'Reference',
      type: FieldType.STRING,
      format: FieldFormat.TEXT,
      source: FieldSource.GENERAL,
      index: 7,
      value: invoice.reference || ''
    },

    // 2. Dates and Terms
    {
      name: 'CreatedAt',
      description: 'Created At',
      type: FieldType.STRING,
      format: FieldFormat.TEXT,
      source: FieldSource.DATES_TERMS,
      index: 10,
      value: invoice.CreatedAt ? new Date(invoice.CreatedAt).toLocaleString() : '',
      readOnly: true
    },
    {
      name: 'UpdatedAt',
      description: 'Updated At',
      type: FieldType.STRING,
      format: FieldFormat.TEXT,
      source: FieldSource.DATES_TERMS,
      index: 11,
      value: invoice.UpdatedAt ? new Date(invoice.UpdatedAt).toLocaleString() : '',
      readOnly: true
    },
    {
      name: 'dueOn',
      description: 'Due Date',
      type: FieldType.DATE,
      format: FieldFormat.DATE,
      source: FieldSource.DATES_TERMS,
      index: 12,
      value: invoice.dueOn ? new Date(invoice.dueOn).toISOString().split('T')[0] : '',
      required: true
    },
    {
      name: 'paidOn',
      description: 'Paid Date',
      type: FieldType.DATE,
      format: FieldFormat.DATE,
      source: FieldSource.DATES_TERMS,
      index: 13,
      value: invoice.paidOn ? new Date(invoice.paidOn).toISOString().split('T')[0] : ''
    },
    {
      name: 'paymentTerms',
      description: 'Payment Terms',
      type: FieldType.STRING,
      format: FieldFormat.TEXT,
      source: FieldSource.DATES_TERMS,
      index: 14,
      value: invoice.paymentTerms || ''
    },

    // 3. Customer/Recipient Information
    {
      name: 'businessId',
      description: 'Business ID',
      type: FieldType.NUMBER,
      format: FieldFormat.NUMBER,
      source: FieldSource.CUSTOMER,
      index: 20,
      value: invoice.businessId || 0
    },
    {
      name: 'locationId',
      description: 'Location ID',
      type: FieldType.NUMBER,
      format: FieldFormat.NUMBER,
      source: FieldSource.CUSTOMER,
      index: 21,
      value: invoice.locationId || 0
    },
    {
      name: 'email',
      description: 'Email',
      type: FieldType.STRING,
      format: FieldFormat.TEXT,
      source: FieldSource.CUSTOMER,
      index: 22,
      value: invoice.email || ''
    },
    {
      name: 'phone',
      description: 'Phone',
      type: FieldType.STRING,
      format: FieldFormat.TEXT,
      source: FieldSource.CUSTOMER,
      index: 23,
      value: invoice.phone || ''
    },
    {
      name: 'address',
      description: 'Address',
      type: FieldType.STRING,
      format: FieldFormat.TEXT,
      source: FieldSource.CUSTOMER,
      index: 24,
      value: invoice.address || ''
    },
    {
      name: 'city',
      description: 'City',
      type: FieldType.STRING,
      format: FieldFormat.TEXT,
      source: FieldSource.CUSTOMER,
      index: 25,
      value: invoice.city || ''
    },
    {
      name: 'province',
      description: 'Province/State',
      type: FieldType.STRING,
      format: FieldFormat.TEXT,
      source: FieldSource.CUSTOMER,
      index: 26,
      value: invoice.province || ''
    },
    {
      name: 'country',
      description: 'Country',
      type: FieldType.STRING,
      format: FieldFormat.TEXT,
      source: FieldSource.CUSTOMER,
      index: 27,
      value: invoice.country || ''
    },

    // 5. Financial Summary
    {
      name: 'subTotal',
      description: 'Subtotal',
      type: FieldType.NUMBER,
      format: FieldFormat.NUMBER,
      source: FieldSource.FINANCIAL,
      index: 50,
      value: invoice.subTotal || 0
    },
    {
      name: 'discountType',
      description: 'Discount Type',
      type: FieldType.STRING,
      format: FieldFormat.TEXT,
      source: FieldSource.FINANCIAL,
      index: 51,
      value: invoice.discountType || ''
    },
    {
      name: 'discountValue',
      description: 'Discount Value',
      type: FieldType.NUMBER,
      format: FieldFormat.NUMBER,
      source: FieldSource.FINANCIAL,
      index: 52,
      value: invoice.discountValue || 0
    },
    {
      name: 'discountTotal',
      description: 'Discount Total',
      type: FieldType.NUMBER,
      format: FieldFormat.NUMBER,
      source: FieldSource.FINANCIAL,
      index: 53,
      value: invoice.discountTotal || 0
    },
    {
      name: 'taxRate',
      description: 'Tax Rate',
      type: FieldType.NUMBER,
      format: FieldFormat.NUMBER,
      source: FieldSource.FINANCIAL,
      index: 54,
      value: invoice.taxRate || 0
    },
    {
      name: 'taxType',
      description: 'Tax Type',
      type: FieldType.STRING,
      format: FieldFormat.TEXT,
      source: FieldSource.FINANCIAL,
      index: 55,
      value: invoice.taxType || ''
    },
    {
      name: 'taxNumber',
      description: 'Tax Number',
      type: FieldType.STRING,
      format: FieldFormat.TEXT,
      source: FieldSource.FINANCIAL,
      index: 56,
      value: invoice.taxNumber || ''
    },
    {
      name: 'tax',
      description: 'Tax Amount',
      type: FieldType.NUMBER,
      format: FieldFormat.NUMBER,
      source: FieldSource.FINANCIAL,
      index: 57,
      value: invoice.tax || 0
    },
    {
      name: 'total',
      description: 'Total',
      type: FieldType.NUMBER,
      format: FieldFormat.NUMBER,
      source: FieldSource.FINANCIAL,
      index: 58,
      value: invoice.total || 0
    },
    {
      name: 'value',
      description: 'Value',
      type: FieldType.NUMBER,
      format: FieldFormat.NUMBER,
      source: FieldSource.FINANCIAL,
      index: 59,
      value: invoice.value || 0
    },

    // 6. Payment Information
    {
      name: 'Paid',
      description: 'Paid',
      type: FieldType.BOOL,
      format: FieldFormat.BOOL,
      source: FieldSource.PAYMENT,
      index: 60,
      value: invoice.Paid || false
    },
    {
      name: 'paidBy',
      description: 'Paid By',
      type: FieldType.STRING,
      format: FieldFormat.TEXT,
      source: FieldSource.PAYMENT,
      index: 61,
      value: invoice.paidBy || ''
    },
    {
      name: 'paymentMethod',
      description: 'Payment Method',
      type: FieldType.STRING,
      format: FieldFormat.SELECT,
      source: FieldSource.PAYMENT,
      index: 62,
      value: invoice.paymentMethod || '',
      options: PAYMENT_METHOD_OPTIONS
    },
    {
      name: 'accountName',
      description: 'Account Name',
      type: FieldType.STRING,
      format: FieldFormat.TEXT,
      source: FieldSource.PAYMENT,
      index: 63,
      value: invoice.accountName || ''
    },
    {
      name: 'accountNumber',
      description: 'Account Number',
      type: FieldType.STRING,
      format: FieldFormat.TEXT,
      source: FieldSource.PAYMENT,
      index: 64,
      value: invoice.accountNumber || ''
    },
    {
      name: 'sortCode',
      description: 'Sort Code',
      type: FieldType.STRING,
      format: FieldFormat.TEXT,
      source: FieldSource.PAYMENT,
      index: 65,
      value: invoice.sortCode || ''
    },

    // 7. Provider Information
    {
      name: 'providerId',
      description: 'Provider ID',
      type: FieldType.NUMBER,
      format: FieldFormat.NUMBER,
      source: FieldSource.PROVIDER,
      index: 70,
      value: invoice.providerId || 0
    },

    // 8. Attachments and Custom Data
    {
      name: 'attachments',
      description: 'Attachments',
      type: FieldType.STRING,
      format: FieldFormat.TEXTAREA,
      source: FieldSource.ATTACHMENTS,
      index: 80,
      value: invoice.attachments ? JSON.stringify(invoice.attachments) : ''
    },
    {
      name: 'data',
      description: 'Custom Data',
      type: FieldType.STRING,
      format: FieldFormat.TEXTAREA,
      source: FieldSource.ATTACHMENTS,
      index: 82,
      value: invoice.data ? JSON.stringify(invoice.data) : ''
    }];

  return baseFields.sort((a, b) => a.index - b.index);
}

interface EditInvoiceDialogProps {
  isOpen: boolean;
  onClose: (open: boolean) => void;
  invoice: IInvoiceRes | null;
  onSave: (updatedInvoice: Partial<IInvoiceRes>) => void;
}

export function EditInvoiceDialog({
  isOpen,
  onClose,
  invoice,
  onSave
}: EditInvoiceDialogProps) {
  const [formData, setFormData] = useState<Record<string, any>>({});
  const [invoiceFields, setInvoiceFields] = useState<IInvoiceField[]>([]);
  const [items, setItems] = useState<any[]>([]);

  useEffect(() => {
    if (invoice) {
      const fields = getInvoiceFields(invoice);
      setInvoiceFields(fields);

      // Initialize form data with current invoice values
      const initialData: Record<string, any> = {};
      fields.forEach(field => {
        initialData[field.name] = field.value;
      });
      setFormData(initialData);

      // Initialize items
      setItems(invoice.items || []);
    }
  }, [invoice]);

  if (!invoice) return null;

  const groupedFields = invoiceFields.reduce((groups, field) => {
    if (!groups[field.source]) {
      groups[field.source] = [];
    }
    groups[field.source].push(field);
    return groups;
  }, {} as Record<string, IInvoiceField[]>);

  const handleFieldChange = (fieldName: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [fieldName]: value
    }));
  };

  const renderField = (field: IInvoiceField) => {
    const fieldId = `field_${field.name}`;
    const value = formData[field.name] || '';

    // Handle read-only fields
    if (field.readOnly) {
      return (
        <Input
          id={fieldId}
          type="text"
          value={value}
          readOnly
          className="bg-muted text-muted-foreground"
        />
      );
    }

    // Handle flags multi-select
    if (field.name === 'flags') {
      return (
        <MultiSelect
          options={FLAG_OPTIONS}
          selected={Array.isArray(value) ? value : []}
          onChange={(flags) => handleFieldChange(field.name, flags)}
          placeholder="Select flags..."
        />
      );
    }

    switch (field.format) {
      case FieldFormat.SELECT:
        return (
          <Select
            value={value}
            onValueChange={(selectedValue) => handleFieldChange(field.name, selectedValue)}
          >
            <SelectTrigger id={fieldId}>
              <SelectValue placeholder={`Select ${field.description.toLowerCase()}`} />
            </SelectTrigger>
            <SelectContent>
              {field.options?.map((option) => (
                <SelectItem key={option.value} value={option.value.toString()}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );

      case FieldFormat.TEXTAREA:
        return (
          <Textarea
            id={fieldId}
            value={value}
            onChange={(e) => handleFieldChange(field.name, e.target.value)}
            placeholder={`Enter ${field.description.toLowerCase()}`}
            rows={3}
          />
        );

      case FieldFormat.DATE:
        return (
          <Input
            id={fieldId}
            type="date"
            value={value}
            onChange={(e) => handleFieldChange(field.name, e.target.value)}
          />
        );

      case FieldFormat.NUMBER:
        return (
          <Input
            id={fieldId}
            type="number"
            step="0.01"
            value={value}
            onChange={(e) => handleFieldChange(field.name, parseFloat(e.target.value) || 0)}
            placeholder="0.00"
          />
        );

      case FieldFormat.BOOL:
        return (
          <div className="flex items-center space-x-2">
            <Checkbox
              id={fieldId}
              checked={value === true || value === 'true'}
              onCheckedChange={(checked) => handleFieldChange(field.name, checked)}
            />
            <Label htmlFor={fieldId} className="text-sm font-normal">
              {field.description}
            </Label>
          </div>
        );

      case FieldFormat.TEXT:
      default:
        return (
          <Input
            id={fieldId}
            type="text"
            value={value}
            onChange={(e) => handleFieldChange(field.name, e.target.value)}
            placeholder={`Enter ${field.description.toLowerCase()}`}
          />
        );
    }
  };

  const handleSave = () => {
    const updatedInvoice: any = {
      ...formData,
      dueOn: formData.dueOn ? new Date(formData.dueOn).toISOString() : invoice.dueOn,
      paidOn: formData.paidOn ? new Date(formData.paidOn).toISOString() : invoice.paidOn,
      items: items
    };
    onSave(updatedInvoice);
    onClose(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Invoice #{invoice.number || invoice.id}</DialogTitle>
        </DialogHeader>

        <div className="py-4">
          <Accordion type="multiple" defaultValue={["General", "DatesTerms", "Customer", "Items", "Financial", "Payment", "Provider", "Attachments"]} className="w-full">

            {/* 1. General Invoice Information */}
            {groupedFields.General && (
              <AccordionItem value="General">
                <AccordionTrigger className="text-lg font-semibold">
                  General Invoice Information
                </AccordionTrigger>
                <AccordionContent>
                  <div className="grid gap-4 py-4">
                    {groupedFields.General.map((field) => (
                      <div key={field.name} className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor={`field_${field.name}`} className="text-right">
                          {field.description}
                          {field.required && <span className="text-red-500 ml-1">*</span>}
                        </Label>
                        <div className="col-span-3">
                          {renderField(field)}
                        </div>
                      </div>
                    ))}
                  </div>
                </AccordionContent>
              </AccordionItem>
            )}

            {/* 2. Dates and Terms */}
            {groupedFields.DatesTerms && (
              <AccordionItem value="DatesTerms">
                <AccordionTrigger className="text-lg font-semibold">
                  Dates and Terms
                </AccordionTrigger>
                <AccordionContent>
                  <div className="grid gap-4 py-4">
                    {groupedFields.DatesTerms.map((field) => (
                      <div key={field.name} className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor={`field_${field.name}`} className="text-right">
                          {field.description}
                          {field.required && <span className="text-red-500 ml-1">*</span>}
                        </Label>
                        <div className="col-span-3">
                          {renderField(field)}
                        </div>
                      </div>
                    ))}
                  </div>
                </AccordionContent>
              </AccordionItem>
            )}

            {/* 3. Customer/Recipient Information */}
            {groupedFields.Customer && (
              <AccordionItem value="Customer">
                <AccordionTrigger className="text-lg font-semibold">
                  Customer/Recipient Information
                </AccordionTrigger>
                <AccordionContent>
                  <div className="grid gap-4 py-4">
                    {groupedFields.Customer.map((field) => (
                      <div key={field.name} className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor={`field_${field.name}`} className="text-right">
                          {field.description}
                          {field.required && <span className="text-red-500 ml-1">*</span>}
                        </Label>
                        <div className="col-span-3">
                          {renderField(field)}
                        </div>
                      </div>
                    ))}
                  </div>
                </AccordionContent>
              </AccordionItem>
            )}

            {/* 4. Invoice Items */}
            {true && (
              <AccordionItem value="Items">
                <AccordionTrigger className="text-lg font-semibold">
                  Invoice Items
                </AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-4 py-4">
                    {items.map((item, index) => (
                      <div key={index} className="border rounded-lg p-4 space-y-4">
                        <div className="flex justify-between items-center">
                          <h4 className="font-medium">Item {index + 1}</h4>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setItems(items.filter((_, i) => i !== index))}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <Label>Description</Label>
                            <Input
                              value={item.description || ''}
                              onChange={(e) => {
                                const newItems = [...items];
                                newItems[index] = { ...item, description: e.target.value };
                                setItems(newItems);
                              }}
                            />
                          </div>
                          <div>
                            <Label>SKU</Label>
                            <Input
                              value={item.sku || ''}
                              onChange={(e) => {
                                const newItems = [...items];
                                newItems[index] = { ...item, sku: e.target.value };
                                setItems(newItems);
                              }}
                            />
                          </div>
                          <div>
                            <Label>Quantity</Label>
                            <Input
                              type="number"
                              value={item.quantity || 0}
                              onChange={(e) => {
                                const newItems = [...items];
                                newItems[index] = { ...item, quantity: parseFloat(e.target.value) || 0 };
                                setItems(newItems);
                              }}
                            />
                          </div>
                          <div>
                            <Label>Unit</Label>
                            <Input
                              value={item.unit || ''}
                              onChange={(e) => {
                                const newItems = [...items];
                                newItems[index] = { ...item, unit: e.target.value };
                                setItems(newItems);
                              }}
                            />
                          </div>
                          <div>
                            <Label>Unit Price</Label>
                            <Input
                              type="number"
                              step="0.01"
                              value={item.unitPrice || 0}
                              onChange={(e) => {
                                const newItems = [...items];
                                newItems[index] = { ...item, unitPrice: parseFloat(e.target.value) || 0 };
                                setItems(newItems);
                              }}
                            />
                          </div>
                          <div>
                            <Label>Discount Amount</Label>
                            <Input
                              type="number"
                              step="0.01"
                              value={item.discountAmount || 0}
                              onChange={(e) => {
                                const newItems = [...items];
                                newItems[index] = { ...item, discountAmount: parseFloat(e.target.value) || 0 };
                                setItems(newItems);
                              }}
                            />
                          </div>
                          <div>
                            <Label>Tax Rate</Label>
                            <Input
                              type="number"
                              step="0.01"
                              value={item.taxRate || 0}
                              onChange={(e) => {
                                const newItems = [...items];
                                newItems[index] = { ...item, taxRate: parseFloat(e.target.value) || 0 };
                                setItems(newItems);
                              }}
                            />
                          </div>
                          <div>
                            <Label>Tax Amount</Label>
                            <Input
                              type="number"
                              step="0.01"
                              value={item.taxAmount || 0}
                              onChange={(e) => {
                                const newItems = [...items];
                                newItems[index] = { ...item, taxAmount: parseFloat(e.target.value) || 0 };
                                setItems(newItems);
                              }}
                            />
                          </div>
                          <div className="col-span-2">
                            <Label>Total Price</Label>
                            <Input
                              type="number"
                              step="0.01"
                              value={item.totalPrice || 0}
                              onChange={(e) => {
                                const newItems = [...items];
                                newItems[index] = { ...item, totalPrice: parseFloat(e.target.value) || 0 };
                                setItems(newItems);
                              }}
                            />
                          </div>
                        </div>
                      </div>
                    ))}
                    <Button
                      variant="outline"
                      onClick={() => setItems([...items, {
                        description: '',
                        sku: '',
                        quantity: 1,
                        unit: '',
                        unitPrice: 0,
                        discountAmount: 0,
                        taxRate: 0,
                        taxAmount: 0,
                        totalPrice: 0
                      }])}
                      className="w-full"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Add Item
                    </Button>
                  </div>
                </AccordionContent>
              </AccordionItem>
            )}

            {/* 5. Financial Summary */}
            {groupedFields.Financial && (
              <AccordionItem value="Financial">
                <AccordionTrigger className="text-lg font-semibold">
                  Financial Summary
                </AccordionTrigger>
                <AccordionContent>
                  <div className="grid gap-4 py-4">
                    {groupedFields.Financial.map((field) => (
                      <div key={field.name} className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor={`field_${field.name}`} className="text-right font-medium">
                          {field.description}
                          {field.required && <span className="text-red-500 ml-1">*</span>}
                        </Label>
                        <div className="col-span-3">
                          {renderField(field)}
                        </div>
                      </div>
                    ))}
                  </div>
                </AccordionContent>
              </AccordionItem>
            )}

            {/* 6. Payment Information */}
            {groupedFields.Payment && (
              <AccordionItem value="Payment">
                <AccordionTrigger className="text-lg font-semibold">
                  Payment Information
                </AccordionTrigger>
                <AccordionContent>
                  <div className="grid gap-4 py-4">
                    {groupedFields.Payment.map((field) => (
                      <div key={field.name} className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor={`field_${field.name}`} className="text-right">
                          {field.description}
                          {field.required && <span className="text-red-500 ml-1">*</span>}
                        </Label>
                        <div className="col-span-3">
                          {renderField(field)}
                        </div>
                      </div>
                    ))}
                  </div>
                </AccordionContent>
              </AccordionItem>
            )}

            {/* 7. Provider Information */}
            {groupedFields.Provider && (
              <AccordionItem value="Provider">
                <AccordionTrigger className="text-lg font-semibold">
                  Provider Information
                </AccordionTrigger>
                <AccordionContent>
                  <div className="grid gap-4 py-4">
                    {groupedFields.Provider.map((field) => (
                      <div key={field.name} className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor={`field_${field.name}`} className="text-right">
                          {field.description}
                          {field.required && <span className="text-red-500 ml-1">*</span>}
                        </Label>
                        <div className="col-span-3">
                          {renderField(field)}
                        </div>
                      </div>
                    ))}
                  </div>
                </AccordionContent>
              </AccordionItem>
            )}

            {/* 8. Attachments and Custom Data */}
            {groupedFields.Attachments && (
              <AccordionItem value="Attachments">
                <AccordionTrigger className="text-lg font-semibold">
                  Attachments and Custom Data
                </AccordionTrigger>
                <AccordionContent>
                  <div className="grid gap-4 py-4">
                    {groupedFields.Attachments.map((field) => (
                      <div key={field.name} className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor={`field_${field.name}`} className="text-right">
                          {field.description}
                          {field.required && <span className="text-red-500 ml-1">*</span>}
                        </Label>
                        <div className="col-span-3">
                          {renderField(field)}
                        </div>
                      </div>
                    ))}
                  </div>
                </AccordionContent>
              </AccordionItem>
            )}

          </Accordion>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onClose(false)}>
            Cancel
          </Button>
          <Button onClick={handleSave}>
            Save Changes
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}