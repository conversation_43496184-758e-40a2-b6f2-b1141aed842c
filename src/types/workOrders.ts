import {IRelatedUser} from './user';

export interface IWorkOrderRes {
  Assets: any[],
  CreatedAt: string,
  ServiceRecords: any[],
  TeamMembers: ITeamMemberWorkOrderRes[],
  UpdatedAt: string,
  business: any,
  businessId: number,
  description: string,
  ended: string,
  fields: any,
  flags: any,
  id: number,
  invoiceId: number,
  location: any,
  locationId: number,
  num: number,
  price: number,
  providerId: number,
  startDate: string,
  started: string,
  status: "invoiced" | "completed" | "in progress" | "scheduled" | "pending" | "cancelled",
  userId: 1030,
}

export interface ITeamMemberWorkOrderRes {
  businessRoleId: number
  id: number
  roleId: number
  user: IRelatedUser
}

export interface IBusinessTeamRes {
  CreatedAt: string
  Email: string
  Name: string
  Photo: string
  UpdatedAt: string
  UpdatedBy: number
  businessId: number
  id: number
  locationId: number
  permissions: string
  roleId: number
  services: any | null
  type: "operator" | "manager" | "owner" | "contractor" | "technician"
}

export interface ILocationTeamRes extends IBusinessTeamRes { }