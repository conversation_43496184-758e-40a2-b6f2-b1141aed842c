import { IRelatedUser } from './';
import { IWorkOrderRes } from './';
import { ICustomerRelatedRes } from './';
import { IRelatedLocation } from './';

export interface IInvoiceRes {
  CreatedAt: string;
  Num: number;
  Paid: boolean;
  UpdatedAt: string;
  accountName: string;
  accountNumber: string;
  address: string;
  attachments: any | null;
  business: ICustomerRelatedRes;
  businessId: number;
  city: string;
  country: string;
  currency: string;
  data: any | null;
  discountTotal: number;
  discountType: string;
  discountValue: number;
  dueOn: string;
  email: string;
  flags: string[];
  id: number;
  fields: any | null;
  location: IRelatedLocation;
  locationId: number;
  name: string;
  notes: string;
  number: string;
  paidBy: string;
  paidOn: string;
  paymentMethod: string;
  paymentTerms: string;
  phone: string;
  provider: IInvoiceProviderRes;
  providerId: number;
  province: string;
  reference: string;
  sortCode: string;
  status: string;
  subTotal: number;
  tax: number;
  taxNumber: string;
  taxRate: number;
  taxType: string;
  terms: string;
  total: number;
  value: number;
  workOrder: IWorkOrderRes;
  items?: IInvoiceItem[];
}

interface IInvoiceItem{
  id: number;
  invoiceId: number;
  description: string;
  sku: string;
  quantity: number;
  unit: string;
  unitPrice: number;
  totalPrice: number;
  discountAmount: number;
  taxRate: number;
  taxAmount: number;
  CreatedAt: string;   // ISO string
  UpdatedAt: string;   // ISO string
}

interface IInvoiceProviderRes {
  Configs: any | null;
  Contacts: any | null;
  Contractors: any | null;
  CreatedAt: string;
  Customers: any | null;
  Roles: any | null;
  Subscriptions: any | null;
  UpdatedAt: string;
  UpdatedBy: number;
  User: IRelatedUser;
  accountName: string;
  accountNumber: string;
  address: string;
  background: string;
  bank: string;
  banner: string;
  categories: any | null;
  city: string;
  colour: string;
  country: string;
  currency: string;
  description: string;
  email: string;
  enabled: boolean;
  facebook: string;
  flags: string[] | null;
  id: number;
  instagram: string;
  latlng: string;
  locale: string;
  locations: any | null;
  name: string;
  nid: string;
  paymentTerms: string;
  phone: string;
  photo: string;
  province: string;
  rating: string;
  session: string;
  signal: string;
  sortCode: string;
  style: string;
  summary: string;
  taxId: string;
  telegram: string;
  twitter: string;
  type: string;
  uid: string;
  userId: number;
  uuid: string;
  website: string;
  whatsapp: string;
  youtube: string;
  zipcode: string;
}

export interface IBatchResponse { //TODO: changue with api response
  id: number;
  num: string;
  status: "draft" | "sent" | "unpaid" | "paid";
  invoiceCount: number;
  totalValue: number;
  createdAt: string;
  Invoices: IInvoiceRes[];
  customFields: any;
}
